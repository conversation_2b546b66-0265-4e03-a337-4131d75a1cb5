{"id": "time-course-platform-env", "name": "Time Course Platform Environment", "values": [{"key": "base_url", "value": "http://localhost:3000", "description": "Base URL for the API Gateway", "type": "default", "enabled": true}, {"key": "auth_token", "value": "", "description": "Current authentication token (set this based on role)", "type": "default", "enabled": true}, {"key": "admin_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.EY9B3fdgEJCvYjcKrk6QRGhopITOb66mt3nRQoadLLU", "description": "Admin user token for testing admin endpoints", "type": "default", "enabled": true}, {"key": "tutor_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.Xsk_7KtYD9hf7IM63kmQc5-QJF4oKFTuw8AriZXgO5k", "description": "Tutor user token for testing tutor endpoints", "type": "default", "enabled": true}, {"key": "student_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************.O-2vd1RZf4xw5F0WRs1mSEmn3x7m6AsLqO0IBUXKk_g", "description": "Student user token for testing student endpoints", "type": "default", "enabled": true}, {"key": "admin_user_id", "value": "68d39dba545d3c08136cb8db", "description": "Admin user ID for testing", "type": "default", "enabled": true}, {"key": "tutor_user_id", "value": "68d39dba545d3c08136cb8dc", "description": "Tutor user ID for testing", "type": "default", "enabled": true}, {"key": "student_user_id", "value": "68d39dba545d3c08136cb8de", "description": "Student user ID for testing", "type": "default", "enabled": true}, {"key": "sample_course_id", "value": "course-id-placeholder", "description": "Sample course ID for testing (replace with actual course ID)", "type": "default", "enabled": true}, {"key": "sample_payment_id", "value": "payment-id-placeholder", "description": "Sample payment ID for testing (replace with actual payment ID)", "type": "default", "enabled": true}, {"key": "sample_session_id", "value": "session-id-placeholder", "description": "Sample coaching session ID for testing (replace with actual session ID)", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}
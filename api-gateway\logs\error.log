{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Auth service proxy error: connect ECONNREFUSED **********:3001","port":3001,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3001\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:20:14.150Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:20:33.425Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:21:33.583Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:21:38.827Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:21:42.608Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:37:19.480Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:37:25.050Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:37:25.942Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Analytics service proxy error: connect ECONNREFUSED **********:3004","port":3004,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3004\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:37:46.498Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Analytics service proxy error: connect ECONNREFUSED **********:3004","port":3004,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3004\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:37:49.035Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Auth service proxy error: connect ECONNREFUSED **********:3001","port":3001,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3001\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:38:58.199Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Auth service proxy error: connect ECONNREFUSED **********:3001","port":3001,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3001\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T06:39:01.406Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Auth service proxy error: connect ECONNREFUSED **********:3001","port":3001,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3001\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T07:03:42.846Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Auth service proxy error: connect ECONNREFUSED **********:3001","port":3001,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3001\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T07:03:44.968Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T07:04:04.937Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T07:04:09.614Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T07:04:10.382Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T07:04:10.568Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T07:04:10.747Z"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Course service proxy error: connect ECONNREFUSED **********:3002","port":3002,"service":"api-gateway","stack":"Error: connect ECONNREFUSED **********:3002\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-09-24T07:04:10.953Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","level":"error","message":"Auth service proxy error: getaddrinfo ENOTFOUND auth-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T07:33:56.258Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"course-service","level":"error","message":"Course service proxy error: getaddrinfo ENOTFOUND course-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND course-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T07:46:24.955Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","level":"error","message":"Auth service proxy error: getaddrinfo ENOTFOUND auth-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T07:54:47.601Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","level":"error","message":"Auth service proxy error: getaddrinfo ENOTFOUND auth-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T07:56:30.199Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","level":"error","message":"Auth service proxy error: getaddrinfo ENOTFOUND auth-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T07:56:40.577Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","level":"error","message":"Auth service proxy error: getaddrinfo ENOTFOUND auth-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T07:57:02.803Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","level":"error","message":"Auth service proxy error: getaddrinfo ENOTFOUND auth-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T07:57:15.562Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","level":"error","message":"Auth service proxy error: getaddrinfo ENOTFOUND auth-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T07:57:56.247Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","level":"error","message":"Auth service proxy error: getaddrinfo ENOTFOUND auth-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T08:01:02.838Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","level":"error","message":"Auth service proxy error: getaddrinfo ENOTFOUND auth-service","service":"api-gateway","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-09-24T08:01:08.024Z"}
